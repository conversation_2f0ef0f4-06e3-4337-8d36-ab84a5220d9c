import { QuotationsForm, QuotationsRow, DocumentsRow, CustomersRow, QuotationsService, StatesRow } from '@/ServerTypes/Default';
import { Decorators, toId, alertDialog, WidgetProps, SaveResponse, getRemoteData } from '@serenity-is/corelib';
import { FinancialYearHelper } from '../FinancialYears/FinancialYearHelper';
import { VerifyAuthorizeDialog } from '../../Common/Helpers/VerifyAuthorizeDialog';
import { ReportHelper } from '@serenity-is/extensions';

@Decorators.registerClass('ReconnBooks.Default.QuotationsDialog')
@Decorators.responsive()
@Decorators.panel()
export class QuotationsDialog extends VerifyAuthorizeDialog<QuotationsRow> {
    protected getFormKey() { return QuotationsForm.formKey; }
    protected getRowDefinition() { return QuotationsRow; }
    protected getService() { return QuotationsService.baseUrl; }

    protected form = new QuotationsForm(this.idPrefix);
    private docType: string;

    private updateTotalTimeout: number;

    constructor(props: WidgetProps<any>) {
        super(props);

        // Debounced update total to avoid multiple calls
        const debouncedUpdateTotal = () => {
            if (this.updateTotalTimeout) {
                clearTimeout(this.updateTotalTimeout);
            }
            this.updateTotalTimeout = window.setTimeout(() => {
                this.updateTotal();
            }, 50); // 50ms debounce
        };

        (this.form.QuotationDetailsList.view as any).onRowsOrCountChanged.subscribe((e: any) => {
            e.stopPropagation();
            debouncedUpdateTotal();
            this.form.QuotationDetailsList.getGrid().focus();

            // Scroll to bottom of the grid
            const grid = this.form.QuotationDetailsList.getGrid();
            const rowCount = grid.getDataLength();
            if (rowCount > 0) {
                grid.scrollRowIntoView(rowCount - 1);
            }
        });

        // Only subscribe to onRowsOrCountChanged to avoid duplicate calls
        // onDataChanged is already handled by ReconnGridEditorBase for summary refresh

        //AutoNumbering for QuotationNo
        this.form.CustomerId.change(() => {
            setTimeout(async () => {
                var CustomerId = toId(this.form.CustomerId.value);
                if (CustomerId != null) {
                    let customer = (await CustomersRow.getLookupAsync()).itemById[CustomerId];
                    this.form.GSTIN.value = customer.GSTIN;
                    this.form.PlaceOfSupplyStateName.value = customer.PlaceOfSupplyStateName;
                    this.form.BillingAddress.value = customer.BillingAddress;
                    this.form.BillingCityCityName.value = customer.BillingCityCityName;
                    this.form.BillingPinCode.value = customer.BillingPinCode;

                    var userSupplyStateId = getRemoteData("UserData").PlaceOfSupplyStateId;
                    this.form.QuotationDetailsList.SetPlaceOfSupply(customer.PlaceOfSupplyId == userSupplyStateId ? true : false);
                }
                else {
                    this.clearCustomerFields();
                }
            }, 100);
        })

        this.form.FinancialYearId.changeSelect2(async () => {
            this.getNextNumber();
        });
    }
    //-----------------AutoNumbering End--------

    private updateTotal(): void {
        let total = 0;
        for (let k of this.form.QuotationDetailsList.view.getItems()) {
            total += k.NetAmount || 0;
        }
        const roundOff = total - Math.floor(total);
        this.form.RoundingOff.value = parseFloat((roundOff >= 0.50 ? 1 - roundOff : -roundOff).toFixed(2));
        this.form.GrandTotal.value = total + this.form.RoundingOff.value;
    }

    protected async afterLoadEntity() {
        super.afterLoadEntity();

        if (this.isNew()) {
            //Autonumbering
            if (!this.form.FinancialYearId?.value) {
                FinancialYearHelper.getCurrentFinancialYearId().then(async currentFinancialYearId => {
                    this.form.FinancialYearId.value = currentFinancialYearId.toString();
                    await this.getNextNumber();
                    this.setDialogsLoadedState();
                });
            }
            else {
                await this.getNextNumber();
                this.setDialogsLoadedState();
            }
        }
        else {
            var placeOfSupplyStateId = (await StatesRow.getLookupAsync()).items.find(x => x.StateName == this.form.PlaceOfSupplyStateName.value)?.StateId;

            var userSupplyStateId = getRemoteData("UserData").PlaceOfSupplyStateId;
            this.form.QuotationDetailsList.SetPlaceOfSupply(placeOfSupplyStateId == userSupplyStateId ? true : false);

            this.setDialogsLoadedState();
        }
    }

    protected updateInterface() {

        // by default cloneButton is hidden in base UpdateInterface method
        super.updateInterface();
        // here we show it if it is edit mode (not new)
        this.cloneButton.toggle(this.isEditMode());
    }

    protected getCloningEntity() {
        let clonedEntity = super.getCloningEntity();
        //clonedEntity.QuotationDate = new Date().toDateString();
        return clonedEntity;
    }

    private async getNextNumber(): Promise<any> {

        if (this.docType == null) {
            this.docType = (await DocumentsRow.getLookupAsync()).items.filter(a => a.DocumentName == "Quotations")[0].DocumentShortName;
        }

        let prefix = this.getNextNumberPrefix(this.docType, this.form.FinancialYearId.text);

        this.form.QuotationNo.value = prefix;
    }

    protected validateBeforeSave() {
        return true
    }

    protected save(callback: (response: SaveResponse) => void) {
        if (!this.form.QuotationDetailsList.value || this.form.QuotationDetailsList.value.length === 0) {
            alertDialog("Quotation cannot be saved because no items have been added. Please add at least one item to proceed.");
            return;
        }
        super.save(callback);
    }

    getToolbarButtons() {
        var buttons = super.getToolbarButtons();

        buttons.push({
            title: 'Print',
            icon: 'fas fa-file-pdf text-danger',
            cssClass: 'print-button',

            onClick: () => {
                ReportHelper.execute({
                    reportKey: 'QuotationReport',
                    params: {
                        ID: this.entityId
                    }
                });
            }
        });
        return buttons;
    }
    private clearCustomerFields() {
        this.form.GSTIN.value = undefined;
        this.form.PlaceOfSupplyStateName.value = undefined;
        this.form.BillingAddress.value = undefined;
        this.form.BillingCityCityName.value = undefined;
        this.form.BillingPinCode.value = undefined;
        this.form.QuotationDetailsList.value = undefined;
    }

    protected updateTitle() {
        this.dialogTitle = "New Quotation";
    }
}

