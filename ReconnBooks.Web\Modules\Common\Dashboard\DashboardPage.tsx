import { faIcon, stringFormat } from "@serenity-is/corelib";
import * as preact from "preact";
import { CalendarCard } from "./calendar/calendar-card";
import { ChatCard } from "./chat/chat-card";
import { ChatSql } from "./chat/chat-sql";
import { OrdersCard } from "./orders/orders-card";
import { SmallCard } from "./shared/small-card";
import { TodoCard } from "./todo/todo-card";
//import {TrafficCard } from "./traffic/traffic-card";
//import { VisitorsCard } from "./visitors/visitors-card";
import { DashboardPageModel } from "../../ServerTypes/Common";

export default function pageInit({ model, nwLinkFormat }: { model: DashboardPageModel, nwLinkFormat: string }) {

    const nwLink = (s: string) => stringFormat(nwLinkFormat, s);

    document.getElementById("DashboardContent").append(<>
            <div class="row">
                <SmallCard caption="Total Customers" icon={faIcon("user-plus")} url={nwLink("Customers")} value={model.CustomerCount} />
                <SmallCard caption="Total No. of Invoices" icon={faIcon("shopping-cart")} url={nwLink("Invoices")} value={model.SalesCount} />
                <SmallCard caption="Invoice Total" icon={faIcon("truck")} url={nwLink("Invoices")} value={<>{model.InvoicesTotal}<sup style="font-size: 20px"></sup></>} />
            </div>
            <div class="row">
                <SmallCard caption="Total Suppliers" icon={faIcon("industry")} url={nwLink("Vendors")} value={model.SuppliersCount}/>
                <SmallCard caption="Total No. of VendorBills" icon={faIcon("file-alt")} url={nwLink("VendorBills")} value={model.VendorBillsCount} />
                <SmallCard caption="VendorBills Total" icon={faIcon("file-invoice-dollar")} url={nwLink("VendorBills")} value={<>{model.VendorBillsTotal}<sup style="font-size: 20px"></sup></>} />
            </div>

        <div class="row">
            <section class="col-lg-7">
                <OrdersCard />

                {/* Chat is implemented via preact just as a demonstration of integrating another lib like preact/react inside jsx-dom tree */}
                <div class="card s-dashboard-card s-chat" ref={el => preact.render(preact.h(ChatCard, {}), el)} />
            </section>
            <section class="col-lg-5">
                {/*<VisitorsCard />*/}
                {/*<TrafficCard />*/}
                <CalendarCard />
                <TodoCard />
            </section>
        </div>

        <div class="row mt-4">
            <section class="col-lg-12">
                {/* SQL Chat Assistant */}
                <div class="card s-dashboard-card s-sql-chat" ref={el => preact.render(preact.h(ChatSql, {}), el)} />
            </section>
        </div>
    </>)
}