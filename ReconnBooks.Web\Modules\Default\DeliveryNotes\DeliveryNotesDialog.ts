import { DeliveryNotesForm, DeliveryNotesRow, DeliveryNotesService, DocumentsRow, CustomersRow, StatesRow, CitiesRow, SupplyTypesRow } from '@/ServerTypes/Default';
import { Decorators, formatDate, toId, getRemoteData, alertDialog, WidgetProps } from '@serenity-is/corelib';
import { FinancialYearHelper } from '../FinancialYears/FinancialYearHelper';
import { VerifyAuthorizeDialog } from '../../Common/Helpers/VerifyAuthorizeDialog';
import { ReportHelper } from '@serenity-is/extensions';

@Decorators.registerClass('ReconnBooks.Default.DeliveryNotesDialog')
@Decorators.responsive()
@Decorators.panel()
export class DeliveryNotesDialog extends VerifyAuthorizeDialog<DeliveryNotesRow> {
    protected getFormKey() { return DeliveryNotesForm.formKey; }
    protected getRowDefinition() { return DeliveryNotesRow; }
    protected getService() { return DeliveryNotesService.baseUrl; }

    protected form = new DeliveryNotesForm(this.idPrefix);
    private docType: string;
    constructor(props: WidgetProps<any>) {
        super(props);

        (this.form.DeliveryNoteDetailsList.view as any).onRowsOrCountChanged.subscribe((e) => {
            e.stopPropagation();
            this.form.DeliveryNoteDetailsList.getGrid().focus();

            const grid = this.form.DeliveryNoteDetailsList.getGrid();
            const rowCount = grid.getDataLength();
            if (rowCount > 0) {
                grid.scrollRowIntoView(rowCount - 1);
            }
        });

        this.form.CustomerId.change(async a => {
            //setTimeout(async () => {
                var CustomerId = toId(this.form.CustomerId.value);
                if (CustomerId != null) {
                    var customer = (await CustomersRow.getLookupAsync()).itemById[CustomerId];
                    this.form.GSTIN.value = customer.GSTIN;
                    this.form.PlaceOfSupplyStateName.value = customer.PlaceOfSupplyStateName;
                    this.form.BillingAddress.value = customer.BillingAddress;
                    this.form.BillingCityCityName.value = customer.BillingCityCityName;
                    this.form.BillingPinCode.value = customer.BillingPinCode;
                    this.form.SupplyTypeId.value = customer.SupplyTypeId.toString();

                    var userSupplyStateId = getRemoteData("UserData").PlaceOfSupplyStateId;
                    this.form.DeliveryNoteDetailsList.SetPlaceOfSupply(customer.PlaceOfSupplyId == userSupplyStateId ? true : false);
                }
                else {
                    this.clearCustomerFields();
                }
            //}, 100);
        })

        this.form.SalesOrderId.changeSelect2(e => {
            if (this.form.SalesOrderId.value === '') {
                // Clear the details in the grid
                this.form.DeliveryNoteDetailsList.value = [];
            }
            else {
                DeliveryNotesService.GetFromSalesOrderDetails({
                    EntityId: toId(this.form.SalesOrderId.value)
                },
                    response => {
                        this.form.DeliveryNoteDetailsList.value = response.Entities;
                    });
            }
        });

        this.form.FinancialYearId.changeSelect2(e => {
            this.getNextNumber();
        });
        this.form.ShipToCustomerId.change(async a => {
            var ShipToCustomerId = toId(this.form.ShipToCustomerId.value);
            if (ShipToCustomerId != null) {
                var shiptocustomer = (await CustomersRow.getLookupAsync()).itemById[ShipToCustomerId];
                this.form.ShippingAddress.value = shiptocustomer.BillingAddress;
                this.form.ShippingCityName.value = shiptocustomer.BillingCityCityName;
                this.form.ShippingPinCode.value = shiptocustomer.BillingPinCode;
                this.form.ShippingGSTIN.value = shiptocustomer.GSTIN;
                this.form.ShippingPlaceOfSupplyStateName.value = shiptocustomer.PlaceOfSupplyStateName;
            }
            else {
                this.clearShipToCustomerFields();
            }
        })
    }

    protected async afterLoadEntity() {
        super.afterLoadEntity();
        if (this.isNew()) {

            this.form.SupplyTypeId.value = (await SupplyTypesRow.getLookupAsync()).items.find(item => item.SetDefault)?.SupplyTypeId.toString();

            if (!this.form.FinancialYearId?.value) {
                FinancialYearHelper.getCurrentFinancialYearId().then(currentFinancialYearId => {
                    this.form.FinancialYearId.value = currentFinancialYearId.toString();
                });
            }
            
            this.getNextNumber();
            this.setDialogsLoadedState();
        }
        else {
            var placeOfSupplyStateId = (await StatesRow.getLookupAsync()).items.find(x => x.StateName == this.form.PlaceOfSupplyStateName.value)?.StateId;

            var userSupplyStateId = getRemoteData("UserData").PlaceOfSupplyStateId;
            this.form.DeliveryNoteDetailsList.SetPlaceOfSupply(placeOfSupplyStateId == userSupplyStateId ? true : false);

            this.setDialogsLoadedState();
        }
    }

    protected updateInterface() {

        super.updateInterface();
        this.cloneButton.toggle(this.isEditMode());
    }

    protected getCloningEntity() {
        var clonedEntity = super.getCloningEntity();
        return clonedEntity;
    }

    private getNextNumber(): any {

        if (this.docType == null) {
            this.docType = DocumentsRow.getLookup().items.filter(a => a.DocumentName == "Delivery Notes")[0].DocumentShortName;
        }

        var prefix = this.getNextNumberPrefix(this.docType, this.form.FinancialYearId.text);

        this.form.DeliveryNoteNo.value = prefix;
    }

    protected validateBeforeSave() {
        if (!this.form.DeliveryNoteDetailsList.value || this.form.DeliveryNoteDetailsList.value.length === 0) {
            alertDialog("Delivery Note cannot be saved because no items have been added. Please add at least one item to proceed.");
            return false;
        }
        return true;
    }

    getToolbarButtons() {
        var buttons = super.getToolbarButtons();

        buttons.push({
            title: 'Print',
            icon: 'fas fa-file-pdf text-danger',
            cssClass: 'print-button',

            onClick: () => {
                ReportHelper.execute({
                    reportKey: 'DeliveryNoteReport',
                    params: {
                        ID: this.entityId
                    }
                });
            }
        });
        return buttons;
    }
    private clearCustomerFields() {
        this.form.GSTIN.value = undefined;
        this.form.PlaceOfSupplyStateName.value = undefined;
        this.form.BillingAddress.value = undefined;
        this.form.BillingCityCityName.value = undefined;
        this.form.BillingPinCode.value = undefined;
        this.form.SupplyTypeId.value = undefined;
        this.form.DeliveryNoteDetailsList.value = undefined;
    }

    private clearShipToCustomerFields() {
        this.form.ShippingAddress.value = undefined;
        this.form.ShippingCityName.value = undefined;
        this.form.ShippingPinCode.value = undefined;
        this.form.ShippingGSTIN.value = undefined;
        this.form.ShippingPlaceOfSupplyStateName.value = undefined;
    }

    protected updateTitle() {
        this.dialogTitle = "New Delivery Note";
    }
}
