import { CustomersRow, StatesRow, DocumentsRow, CitiesRow, SupplyTypesRow } from '@/ServerTypes/Default';
import { InvoicesForm, InvoicesRow, InvoicesService } from '@/ServerTypes/Default';
import { Decorators, toId, getRemoteData, alertDialog, WidgetProps, indexOf, confirmDialog } from '@serenity-is/corelib';
import { FinancialYearHelper } from '../FinancialYears/FinancialYearHelper';
import { VerifyAuthorizeDialog } from '../../Common/Helpers/VerifyAuthorizeDialog';
import { ReportHelper } from '@serenity-is/extensions';
import { InvoiceCancelDialog } from './InvoiceCancel/InvoiceCancelDialog';
import { PrintOptionsDialog } from './PrintOptions/PrintOptionsDialog';
import { TransactionStatus } from '../../ServerTypes/Modules';

@Decorators.registerClass('ReconnBooks.Default.InvoicesDialog')
@Decorators.responsive()
@Decorators.panel()

export class InvoicesDialog extends VerifyAuthorizeDialog<InvoicesRow> {
    protected getFormKey() { return InvoicesForm.formKey; }
    protected getRowDefinition() { return InvoicesRow; }
    protected getService() { return InvoicesService.baseUrl; }

    //--???--
    protected form = new InvoicesForm(this.idPrefix);
    private docType: string;

    constructor(props: WidgetProps<any>) {
        super(props);

        (this.form.InvoiceDetailsList.view as any).onRowsOrCountChanged.subscribe((e) => {
            e.stopPropagation();
            this.updateTotal();
            this.form.InvoiceDetailsList.getGrid().focus();

            const grid = this.form.InvoiceDetailsList.getGrid();
            const rowCount = grid.getDataLength();
            if (rowCount > 0) {
                grid.scrollRowIntoView(rowCount - 1);
            }

        });

        (this.form.InvoiceDetailsList.view as any).onDataChanged.subscribe((e) => {
            e.preventDefault = false;
            this.updateTotal();
            // Add your custom logic here
        });


        //--Fetching Customer Billing Address--

        this.form.CustomerId.change(async a => {
            //setTimeout(async () => {
                var CustomerId = toId(this.form.CustomerId.value);
                if (CustomerId != null) {
                    var customer = (await CustomersRow.getLookupAsync()).itemById[CustomerId];
                    this.form.GSTIN.value = customer.GSTIN;
                    this.form.PlaceOfSupplyStateName.value = customer.PlaceOfSupplyStateName;
                    this.form.BillingAddress.value = customer.BillingAddress;
                    this.form.BillingCityCityName.value = customer.BillingCityCityName;
                    this.form.BillingPinCode.value = customer.BillingPinCode;
                    this.form.SupplyTypeId.value = customer.SupplyTypeId.toString();

                    var userSupplyStateId = getRemoteData("UserData").PlaceOfSupplyStateId;
                    this.form.InvoiceDetailsList.SetPlaceOfSupply(customer.PlaceOfSupplyId == userSupplyStateId ? true : false);
                }
                else {
                    this.clearCustomerFields();
                }
            //}, 100);
        })

        this.form.ProformaInvoiceId.changeSelect2(e => {
            if (this.form.ProformaInvoiceId.value === '') {
                // Clear the details in the grid
                this.form.InvoiceDetailsList.value = [];
            }
            else {
                InvoicesService.GetFromProformaInvoiceDetails({
                    EntityId: toId(this.form.ProformaInvoiceId.value)
                },
                    response => {
                        this.form.InvoiceDetailsList.value = response.Entities;
                    });
            }
        });

        //--Financial Year--

        this.form.FinancialYearId.changeSelect2(e => {
            this.getNextNumber();
        });

        this.form.ShipToCustomerId.change(async a => {
            var ShipToCustomerId = toId(this.form.ShipToCustomerId.value);
            if (ShipToCustomerId != null) {
                var shiptocustomer = (await CustomersRow.getLookupAsync()).itemById[ShipToCustomerId];
                this.form.ShippingAddress.value = shiptocustomer.BillingAddress;
                this.form.ShippingCityName.value = shiptocustomer.BillingCityCityName;
                this.form.ShippingPinCode.value = shiptocustomer.BillingPinCode;
                this.form.ShippingGSTIN.value = shiptocustomer.GSTIN;
                this.form.ShippingPlaceOfSupplyStateName.value = shiptocustomer.PlaceOfSupplyStateName;
            }
            else {
                this.clearShipToCustomerFields();
            }
        })
    }
    //--Total Amount Calculation--

    private updateTotal(): void {
        let total = 0;
        for (let item of this.form.InvoiceDetailsList.view.getItems()) {
            total += item.NetAmount || 0;
        }
        this.form.InvoiceAmount.value = total;
        const roundOff = total - Math.floor(total);
        this.form.RoundingOff.value = parseFloat((roundOff >= 0.50 ? 1 - roundOff : -roundOff).toFixed(2));
        this.form.GrandTotal.value = this.form.InvoiceAmount.value + this.form.RoundingOff.value;
    }

    protected async afterLoadEntity() {
        super.afterLoadEntity();

        if (this.isNew()) {

            this.form.SupplyTypeId.value = (await SupplyTypesRow.getLookupAsync()).items.find(item => item.SetDefault)?.SupplyTypeId.toString();

            if (!this.form.FinancialYearId?.value) {
                FinancialYearHelper.getCurrentFinancialYearId().then(currentFinancialYearId => {
                    this.form.FinancialYearId.value = currentFinancialYearId.toString();
                });
            }

            this.getNextNumber();
            this.setDialogsLoadedState(); //to save the form when there are values
        }
        else {
            var placeOfSupplyStateId = (await StatesRow.getLookupAsync()).items.find(x => x.StateName == this.form.PlaceOfSupplyStateName.value)?.StateId;

            var userSupplyStateId = getRemoteData("UserData").PlaceOfSupplyStateId;
            this.form.InvoiceDetailsList.SetPlaceOfSupply(placeOfSupplyStateId == userSupplyStateId ? true : false);

            this.setDialogsLoadedState(); //to save the form when there are values

            this.dialogTitle = "Edit Invoice (" + this.form.InvoiceNo.value + ")";
        }
    }

    //--Cloning a Document (Save As)--
    protected updateInterface() {
        super.updateInterface();

        this.cloneButton.toggle(this.isEditMode());
    }

    protected getCloningEntity() {
        var clonedEntity = super.getCloningEntity();
        return clonedEntity;
    }

    //--Document Number Generation--
    private getNextNumber(): any {
        var clientCode = getRemoteData('UserData').ClientCode;
        var prefix = ((clientCode) || "") + "/";

        try {
            prefix += this.form.FinancialYearId.text;
        }
        catch (Error) {
            alertDialog("There seems to be error in Invoices Dialog");
        }
        this.form.InvoiceNo.value = prefix;
    }

    protected validateBeforeSave() {
        if (!this.form.InvoiceDetailsList.value || this.form.InvoiceDetailsList.value.length === 0) {
            alertDialog("Invoice cannot be saved because no items have been added. Please add at least one item to proceed.");
            return false;
        }
        return true;
    }

    getToolbarButtons() {
        var buttons = super.getToolbarButtons();

        //Remove Delete Button. we will think of adding it later as per need.
        // buttons.splice(indexOf(buttons, x => x.cssClass == "delete-button"), 1)

        buttons.push({
            title: 'Print',
            icon: 'fas fa-file-pdf text-danger',
            cssClass: 'print-button',

            onClick: () => {
                // Open the print options dialog
                const printOptionsDialog = new PrintOptionsDialog(this.entityId);
                printOptionsDialog.dialogOpen();
            }
        });

        buttons.push({
            title: 'Invoice Cancellation',
            cssClass: 'delete-button',
            onClick: () => {
                confirmDialog("Are you sure you want to cancel this Invoice?", () => {
                    var invoiceCancelDialog = new InvoiceCancelDialog(this.entity.InvoiceId);
                    invoiceCancelDialog.dialogTitle = "Reason for cancellation";
                    invoiceCancelDialog.returnData = (cancelReason) => {
                        this.form.CancelReason.value = cancelReason;
                        this.form.TransactionStatus.value = TransactionStatus.Cancelled.toString();
                        this.save(() => {
                            this.dialogClose("save-and-close");
                            alertDialog("Invoice cancelled!");
                        });

                    }
                    invoiceCancelDialog.dialogOpen();
                })
            }
        });

        return buttons;
    }
    private clearCustomerFields() {
        this.form.GSTIN.value = undefined;
        this.form.PlaceOfSupplyStateName.value = undefined;
        this.form.BillingAddress.value = undefined;
        this.form.BillingCityCityName.value = undefined;
        this.form.BillingPinCode.value = undefined;
        this.form.SupplyTypeId.value = undefined;
        this.form.InvoiceDetailsList.value = undefined;
    }

    private clearShipToCustomerFields() {
        this.form.ShippingAddress.value = undefined;
        this.form.ShippingCityName.value = undefined;
        this.form.ShippingPinCode.value = undefined;
        this.form.ShippingGSTIN.value = undefined;
        this.form.ShippingPlaceOfSupplyStateName.value = undefined;
    }

    protected updateTitle() {
        this.dialogTitle = "New Invoice";
    }
}
