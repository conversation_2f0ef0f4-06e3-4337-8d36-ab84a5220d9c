using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using ReconnBooks.Common.ChatSql.Models;
using ReconnBooks.Common.ChatSql.Services;

namespace ReconnBooks.Web.Modules.Common.ChatSql
{
    [Route("api/chat-sql")]
    [ApiController]
    public class ChatSqlController : ControllerBase
    {
        private readonly IChatSqlService _chatSqlService;
        private readonly ILogger<ChatSqlController> _logger;

        public ChatSqlController(IChatSqlService chatSqlService, ILogger<ChatSqlController> logger)
        {
            _chatSqlService = chatSqlService;
            _logger = logger;
        }

        [HttpGet("schema")]
        public async Task<IActionResult> GetSchema()
        {
            try
            {
                var schema = await _chatSqlService.GetSchemaAsync();
                return Ok(schema);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving database schema");
                return StatusCode(500, new { error = "Failed to retrieve database schema" });
            }
        }

        [HttpPost("query")]
        public async Task<IActionResult> Query([FromBody] ChatSqlRequest request)
        {
            try
            {
                if (request == null || string.IsNullOrWhiteSpace(request.Message))
                {
                    return BadRequest(new { error = "Message is required" });
                }

                var response = await _chatSqlService.ProcessQueryAsync(request);

                if (response.Success)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing chat SQL query: {Message}", request?.Message);
                return StatusCode(500, new { error = "An error occurred while processing your request" });
            }
        }

        [HttpPost("refresh-schema")]
        public async Task<IActionResult> RefreshSchema()
        {
            try
            {
                await _chatSqlService.RefreshSchemaAsync();
                return Ok(new { message = "Schema refreshed successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing database schema");
                return StatusCode(500, new { error = "Failed to refresh database schema" });
            }
        }

        [HttpGet("status")]
        public async Task<IActionResult> GetStatus()
        {
            try
            {
                var isAvailable = await _chatSqlService.IsServiceAvailableAsync();
                return Ok(new {
                    isAvailable,
                    message = isAvailable ? "Service is available" : "Service is not configured or unavailable"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking service status");
                return StatusCode(500, new { error = "Failed to check service status" });
            }
        }

        [HttpGet("test")]
        public async Task<IActionResult> Test()
        {
            try
            {
                // Test basic functionality without AI
                var schema = await _chatSqlService.GetSchemaAsync();
                var isAvailable = await _chatSqlService.IsServiceAvailableAsync();

                return Ok(new {
                    schemaTableCount = schema.Tables.Count,
                    databaseName = schema.DatabaseName,
                    isAiAvailable = isAvailable,
                    message = "ChatSQL service is working correctly"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing ChatSQL service");
                return StatusCode(500, new { error = ex.Message });
            }
        }
    }
}
