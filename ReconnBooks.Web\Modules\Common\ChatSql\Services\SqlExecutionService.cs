using System.Data;
using System.Diagnostics;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Serenity.Data;
using ReconnBooks.Common.ChatSql.Models;

namespace ReconnBooks.Common.ChatSql.Services
{
    public interface ISqlExecutionService
    {
        Task<QueryValidationResult> ValidateQueryAsync(string sqlQuery);
        Task<object> ExecuteQueryAsync(string sqlQuery);
        Task<ChatSqlMetadata> GetQueryMetadataAsync(string sqlQuery, object result, int executionTimeMs);
    }

    public class SqlExecutionService : ISqlExecutionService
    {
        private readonly ISqlConnections _connections;
        private readonly IOptions<ChatSqlConfiguration> _config;
        private readonly IDatabaseSchemaService _schemaService;
        private readonly ILogger<SqlExecutionService> _logger;

        public SqlExecutionService(
            ISqlConnections connections,
            IOptions<ChatSqlConfiguration> config,
            IDatabaseSchemaService schemaService,
            ILogger<SqlExecutionService> logger)
        {
            _connections = connections;
            _config = config;
            _schemaService = schemaService;
            _logger = logger;
        }

        public async Task<QueryValidationResult> ValidateQueryAsync(string sqlQuery)
        {
            var result = new QueryValidationResult { IsValid = true };

            try
            {
                // Basic syntax validation
                if (string.IsNullOrWhiteSpace(sqlQuery))
                {
                    result.IsValid = false;
                    result.ErrorMessage = "Query cannot be empty";
                    return result;
                }

                // Check for blocked keywords
                var blockedKeywords = _config.Value.BlockedKeywords;
                foreach (var keyword in blockedKeywords)
                {
                    if (Regex.IsMatch(sqlQuery, $@"\b{Regex.Escape(keyword)}\b", RegexOptions.IgnoreCase))
                    {
                        result.IsValid = false;
                        result.ErrorMessage = $"Query contains blocked keyword: {keyword}";
                        return result;
                    }
                }

                // Ensure it's a SELECT statement
                if (!Regex.IsMatch(sqlQuery.Trim(), @"^\s*SELECT\b", RegexOptions.IgnoreCase))
                {
                    result.IsValid = false;
                    result.ErrorMessage = "Only SELECT statements are allowed";
                    return result;
                }

                // Check for multiple statements (basic protection against SQL injection)
                if (sqlQuery.Count(c => c == ';') > 1 ||
                    (sqlQuery.Contains(';') && !sqlQuery.TrimEnd().EndsWith(";")))
                {
                    result.IsValid = false;
                    result.ErrorMessage = "Multiple statements are not allowed";
                    return result;
                }

                // Validate table access
                var tableValidation = await ValidateTableAccessAsync(sqlQuery);
                if (!tableValidation.IsValid)
                {
                    result.IsValid = false;
                    result.ErrorMessage = tableValidation.ErrorMessage;
                    return result;
                }

                // Add safety modifications
                var modifiedQuery = AddSafetyLimits(sqlQuery);
                if (modifiedQuery != sqlQuery)
                {
                    result.ModifiedQuery = modifiedQuery;
                    result.WasModified = true;
                    result.ModificationReason = "Added safety limits to prevent excessive resource usage";
                    result.Warnings.Add("Query was modified to include result limits for safety");
                }

                // Validate syntax by attempting to parse (dry run)
                await ValidateQuerySyntaxAsync(result.ModifiedQuery ?? sqlQuery);

                _logger.LogInformation("Query validation successful");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Query validation failed: {Query}", sqlQuery);
                result.IsValid = false;
                result.ErrorMessage = $"Query validation error: {ex.Message}";
                return result;
            }
        }

        public async Task<object> ExecuteQueryAsync(string sqlQuery)
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                using var connection = _connections.NewByKey("Default");
                await connection.OpenAsync();

                using var command = connection.CreateCommand();
                command.CommandText = sqlQuery;
                command.CommandTimeout = _config.Value.MaxQueryExecutionTimeSeconds;

                using var reader = await command.ExecuteReaderAsync();

                var results = new List<Dictionary<string, object>>();
                var columnNames = new List<string>();

                // Get column names
                for (int i = 0; i < reader.FieldCount; i++)
                {
                    columnNames.Add(reader.GetName(i));
                }

                // Read data
                int rowCount = 0;
                while (await reader.ReadAsync() && rowCount < _config.Value.MaxResultRows)
                {
                    var row = new Dictionary<string, object>();
                    for (int i = 0; i < reader.FieldCount; i++)
                    {
                        var value = reader.IsDBNull(i) ? null : reader.GetValue(i);
                        row[columnNames[i]] = value;
                    }
                    results.Add(row);
                    rowCount++;
                }

                stopwatch.Stop();

                _logger.LogInformation("Query executed successfully. Rows: {RowCount}, Time: {ExecutionTime}ms",
                    rowCount, stopwatch.ElapsedMilliseconds);

                return new
                {
                    Data = results,
                    ColumnNames = columnNames,
                    RowCount = rowCount,
                    ExecutionTimeMs = stopwatch.ElapsedMilliseconds,
                    HasMoreRows = rowCount >= _config.Value.MaxResultRows
                };
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Query execution failed: {Query}", sqlQuery);
                throw new InvalidOperationException($"Query execution failed: {ex.Message}", ex);
            }
        }

        public async Task<ChatSqlMetadata> GetQueryMetadataAsync(string sqlQuery, object result, int executionTimeMs)
        {
            var metadata = new ChatSqlMetadata
            {
                ExecutionTimeMs = executionTimeMs
            };

            try
            {
                if (result is { } resultObj)
                {
                    var resultType = resultObj.GetType();
                    var dataProperty = resultType.GetProperty("Data");
                    var columnNamesProperty = resultType.GetProperty("ColumnNames");
                    var rowCountProperty = resultType.GetProperty("RowCount");

                    if (dataProperty?.GetValue(resultObj) is IEnumerable<object> data)
                    {
                        metadata.RowCount = data.Count();
                    }

                    if (rowCountProperty?.GetValue(resultObj) is int rowCount)
                    {
                        metadata.RowCount = rowCount;
                    }

                    if (columnNamesProperty?.GetValue(resultObj) is IEnumerable<string> columnNames)
                    {
                        metadata.ColumnNames = columnNames.ToArray();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error extracting query metadata");
            }

            return metadata;
        }

        private async Task<QueryValidationResult> ValidateTableAccessAsync(string sqlQuery)
        {
            var result = new QueryValidationResult { IsValid = true };

            try
            {
                // Extract table names from the query (basic regex approach)
                var tablePattern = @"\bFROM\s+(?:\[?(\w+)\]?\.)?(?:\[?(\w+)\]?)|JOIN\s+(?:\[?(\w+)\]?\.)?(?:\[?(\w+)\]?)";
                var matches = Regex.Matches(sqlQuery, tablePattern, RegexOptions.IgnoreCase);

                var referencedTables = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

                foreach (Match match in matches)
                {
                    // Get table name (could be in group 2 or 4 depending on whether schema is specified)
                    var tableName = match.Groups[2].Success ? match.Groups[2].Value : match.Groups[4].Value;
                    if (!string.IsNullOrEmpty(tableName))
                    {
                        referencedTables.Add(tableName);
                    }
                }

                // Check if all referenced tables are allowed
                foreach (var tableName in referencedTables)
                {
                    if (!await _schemaService.IsTableAllowedAsync(tableName))
                    {
                        result.IsValid = false;
                        result.ErrorMessage = $"Access to table '{tableName}' is not allowed";
                        return result;
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error validating table access");
                result.IsValid = false;
                result.ErrorMessage = "Unable to validate table access";
                return result;
            }
        }

        private string AddSafetyLimits(string sqlQuery)
        {
            var query = sqlQuery.Trim();

            // Remove trailing semicolon if present
            if (query.EndsWith(";"))
            {
                query = query.Substring(0, query.Length - 1).Trim();
            }

            // Check if query already has TOP clause
            if (!Regex.IsMatch(query, @"\bTOP\s+\d+\b", RegexOptions.IgnoreCase))
            {
                // Add TOP clause after SELECT
                var selectMatch = Regex.Match(query, @"^(\s*SELECT\s+)", RegexOptions.IgnoreCase);
                if (selectMatch.Success)
                {
                    var maxRows = Math.Min(_config.Value.MaxResultRows, 1000);
                    query = selectMatch.Groups[1].Value + $"TOP {maxRows} " +
                           query.Substring(selectMatch.Length);
                }
            }

            return query;
        }

        private async Task ValidateQuerySyntaxAsync(string sqlQuery)
        {
            try
            {
                using var connection = _connections.NewByKey("Default");
                await connection.OpenAsync();

                using var command = connection.CreateCommand();
                command.CommandText = $"SET PARSEONLY ON; {sqlQuery}; SET PARSEONLY OFF;";
                await command.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Query syntax validation failed: {ex.Message}", ex);
            }
        }
    }
}
