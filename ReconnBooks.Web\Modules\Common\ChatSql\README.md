# Natural Language SQL Assistant - Natural Language to SQL Query Conversion

This service provides natural language to SQL query conversion functionality for the ReconnBooks dashboard using OpenAI integration. Users can ask questions in plain English and get SQL queries executed against their database.

## Features

- **Natural Language Processing**: Convert plain English questions to SQL queries
- **Database Schema Caching**: Efficient schema retrieval and caching for better AI context
- **Query Validation**: Security-focused validation to prevent harmful queries
- **Safe Execution**: Controlled SQL execution with result limits and timeouts
- **Conversation Context**: Maintains conversation history for better query understanding
- **Real-time Chat Interface**: User-friendly chat interface integrated into the dashboard

## Architecture

### Backend Services

1. **ChatSqlService** - Main orchestration service
2. **DatabaseSchemaService** - Database schema retrieval and caching
3. **AiSqlService** - AI/LLM integration for natural language processing
4. **SqlExecutionService** - Safe SQL query validation and execution

### Frontend Components

1. **ChatSql.tsx** - Main chat interface component
2. **ChatSqlService.ts** - TypeScript service for API communication

## Configuration

### appsettings.json

```json
{
  "ChatSql": {
    "OpenAI": {
      "ApiKey": "your-openai-api-key",
      "Model": "gpt-4",
      "MaxTokens": 1000,
      "Temperature": 0.1
    },
    "MaxQueryExecutionTimeSeconds": 30,
    "AllowedTables": [],
    "BlockedKeywords": ["DROP", "DELETE", "UPDATE", "INSERT", "ALTER", "CREATE", "TRUNCATE", "EXEC", "EXECUTE"],
    "MaxResultRows": 1000
  }
}
```

### Configuration Options

- **AllowedTables**: List of tables users can query (empty = all tables allowed)
- **BlockedKeywords**: SQL keywords that are blocked for security
- **MaxResultRows**: Maximum number of rows returned in query results
- **MaxQueryExecutionTimeSeconds**: Timeout for SQL query execution

## Setup Instructions

### 1. Install Dependencies

The OpenAI package is already added to the project:

```xml
<PackageReference Include="OpenAI" Version="2.1.0" />
```

### 2. Configure OpenAI Service

1. Get an API key from [OpenAI](https://platform.openai.com/api-keys)
2. Set the `ChatSql:OpenAI:ApiKey` in your configuration

### 3. Database Permissions

Ensure the application has read access to:
- Information schema tables
- All tables you want to allow querying
- System tables for metadata retrieval

### 4. Security Configuration

Review and customize:
- `AllowedTables`: Restrict access to specific tables if needed
- `BlockedKeywords`: Add additional keywords to block if required
- `MaxResultRows`: Adjust based on your performance requirements

## Usage

### Dashboard Integration

The ChatSQL component is automatically integrated into the dashboard. Users can:

1. Type natural language questions in the chat interface
2. View generated SQL queries
3. See query results in a formatted table
4. Maintain conversation context for follow-up questions

### Example Queries

- "Show me all customers from last month"
- "What are the top 5 products by sales?"
- "How many invoices were created this year?"
- "List all vendors with their contact information"
- "Show me the total revenue by month"

### API Endpoints

- `GET /api/chat-sql/schema` - Get database schema information
- `POST /api/chat-sql/query` - Process natural language query
- `POST /api/chat-sql/refresh-schema` - Refresh cached schema
- `GET /api/chat-sql/status` - Check service availability

## Security Features

### Query Validation
- Only SELECT statements are allowed
- Blocked keywords prevent dangerous operations
- Table access can be restricted
- Query syntax validation before execution

### Safe Execution
- Query timeouts prevent long-running queries
- Result row limits prevent excessive data retrieval
- Connection pooling and proper resource disposal
- Error handling and logging

### AI Safety
- System prompts enforce security rules
- Response validation ensures only valid SQL is generated
- Conversation context is limited to prevent prompt injection

## Troubleshooting

### Service Not Available
1. Check AI service configuration (API keys, endpoints)
2. Verify network connectivity to AI service
3. Check application logs for initialization errors

### Query Generation Issues
1. Verify database schema is accessible
2. Check if tables are in the allowed list
3. Review AI service logs for errors

### Performance Issues
1. Adjust `MaxResultRows` to limit result size
2. Increase `MaxQueryExecutionTimeSeconds` if needed
3. Monitor database performance and indexing

## Development

### Adding New Features
1. Extend the service interfaces in the `Services` folder
2. Update the models in the `Models` folder
3. Modify the frontend component as needed
4. Update API endpoints in the controller

### Testing
1. Test with various natural language inputs
2. Verify security restrictions work correctly
3. Test error handling scenarios
4. Performance test with large datasets

## Support

For issues or questions:
1. Check the application logs for detailed error messages
2. Verify configuration settings
3. Test AI service connectivity independently
4. Review database permissions and schema access
