import { PurchaseOrdersForm, PurchaseOrdersRow, PurchaseOrdersService, VendorsRow, TcsRatesRow, TdsRatesRow, CitiesRow, CustomersRow, SupplyTypesRow } from '@/ServerTypes/Default';
import { Decorators, formatDate, toId, getRemoteData, alertDialog, WidgetProps } from '@serenity-is/corelib';
import { DocumentsRow, StatesRow } from '@/ServerTypes/Default';
import { FinancialYearHelper } from '../FinancialYears/FinancialYearHelper';
import { VerifyAuthorizeDialog } from '../../Common/Helpers/VerifyAuthorizeDialog';
import { ReportHelper } from '@serenity-is/extensions';

@Decorators.registerClass('ReconnBooks.Default.PurchaseOrdersDialog')
@Decorators.panel()
export class PurchaseOrdersDialog extends VerifyAuthorizeDialog<PurchaseOrdersRow> {
    protected getFormKey() { return PurchaseOrdersForm.formKey; }
    protected getRowDefinition() { return PurchaseOrdersRow; }
    protected getService() { return PurchaseOrdersService.baseUrl; }

    protected form = new PurchaseOrdersForm(this.idPrefix);
    private docType: string;
    private netTaxableAmount: number;

    constructor(props: WidgetProps<any>) {
        super(props);

        (this.form.PurchaseOrderDetailsList.view as any).onRowsOrCountChanged.subscribe(async (e) => {
            e.stopPropagation();
            await this.calculateTaxesAndGrandTotal()
            this.form.PurchaseOrderDetailsList.getGrid().focus();

            const grid = this.form.PurchaseOrderDetailsList.getGrid();
            const rowCount = grid.getDataLength();
            if (rowCount > 0) {
                grid.scrollRowIntoView(rowCount - 1);
            }
        });

        (this.form.PurchaseOrderDetailsList.view as any).onDataChanged.subscribe(async (e) => {
            e.preventDefault = false;
            await this.calculateTaxesAndGrandTotal()
        });

        //--Fetching Customer Billing Address--

        this.form.VendorId.change(async a => {
            //setTimeout(async () => {
                var VendorId = toId(this.form.VendorId.value);
                if (VendorId != null) {
                    var Vendor = (await VendorsRow.getLookupAsync()).itemById[VendorId];
                    this.form.GSTIN.value = Vendor.GSTIN;
                    this.form.PlaceOfSupplyStateName.value = Vendor.PlaceOfSupplyStateName;
                    this.form.BillingAddress.value = Vendor.BillingAddress;
                    this.form.BillingCityCityName.value = Vendor.BillingCityCityName;
                    this.form.BillingPinCode.value = Vendor.BillingPinCode;
                    this.form.SupplyTypeId.value = Vendor.SupplyTypeId.toString();

                    var userSupplyStateId = getRemoteData("UserData").PlaceOfSupplyStateId;
                    this.form.PurchaseOrderDetailsList.SetPlaceOfSupply(Vendor.PlaceOfSupplyId == userSupplyStateId ? true : false);
                }
                else {
                    this.clearVendorFields();
                }
            //}, 100);
        })

        this.form.TDSRateId.changeSelect2(async a => {
            await this.calculateTDSAmount();
            this.calculateRoundedOffGrandTotal()

        })

        this.form.TCSRateId.changeSelect2(async a => {
            await this.calculateTCSAmount();
            this.calculateRoundedOffGrandTotal()
        })

        //--Financial Year--

        this.form.FinancialYearId.changeSelect2(e => {
            this.getNextNumber();
        });
        this.form.ShipToCustomerId.change(async a => {
            var ShipToCustomerId = toId(this.form.ShipToCustomerId.value);
            if (ShipToCustomerId != null) {
                var shiptocustomer = (await CustomersRow.getLookupAsync()).itemById[ShipToCustomerId];
                this.form.ShippingAddress.value = shiptocustomer.BillingAddress;
                this.form.ShippingCityName.value = shiptocustomer.BillingCityCityName;
                this.form.ShippingPinCode.value = shiptocustomer.BillingPinCode;
                this.form.ShippingGSTIN.value = shiptocustomer.GSTIN;
                this.form.ShippingPlaceOfSupplyStateName.value = shiptocustomer.PlaceOfSupplyStateName;
            }
            else {
                this.clearShipToCustomerFields();
            }
        })
    }

    private async calculateTDSAmount() {
        var tdsRateId = toId(this.form.TDSRateId.value);
        if (tdsRateId != null) {
            var tdsRateRow = (await TdsRatesRow.getLookupAsync()).itemById[tdsRateId];
            if (tdsRateRow != null) {
                this.form.TDSAmount.value = (tdsRateRow.TDSRate * this.netTaxableAmount) / 100;
            }
        }
    }

    private async calculateTCSAmount() {
        let tcsRateId = toId(this.form.TCSRateId.value);
        if (tcsRateId != null) {
            let tcsRateRow = (await TcsRatesRow.getLookupAsync()).itemById[tcsRateId];
            if (tcsRateRow != null) {
                let tcsAmount = (tcsRateRow.TCSRate * this.netTaxableAmount) / 100;
                this.form.TCSAmount.value = tcsAmount;
            }
        }
    }

    //--Total Amount Calculation--
    private calculateRoundedOffGrandTotal(): void {

        var total = this.form.POAmount.value - (this.form.TDSAmount.value ?? 0) - (this.form.TCSAmount.value ?? 0);

        const roundOff = total - Math.floor(total);
        this.form.RoundingOff.value = parseFloat((roundOff >= 0.50 ? 1 - roundOff : -roundOff).toFixed(2));
        this.form.GrandTotal.value = total + this.form.RoundingOff.value;
    }

    private calculatPOAmountAndNetTaxableAmounts(): void {
        let netPOAmountTotal = 0;
        let netTaxableAmountTotal = 0;
        for (let item of this.form.PurchaseOrderDetailsList.view.getItems()) {
            netPOAmountTotal += item.NetAmount || 0;
            netTaxableAmountTotal += item.NetTaxableAmount || 0;
        }
        this.form.POAmount.value = netPOAmountTotal;
        this.netTaxableAmount = netTaxableAmountTotal;
    }

    private async calculateTaxesAndGrandTotal() {
        this.calculatPOAmountAndNetTaxableAmounts();
        await this.calculateTDSAmount();
        await this.calculateTCSAmount();
        this.calculateRoundedOffGrandTotal();
    }

    protected async afterLoadEntity() {
        super.afterLoadEntity();
        if (this.isNew()) {

            this.form.SupplyTypeId.value = (await SupplyTypesRow.getLookupAsync()).items.find(item => item.SetDefault)?.SupplyTypeId.toString();

            if (!this.form.FinancialYearId?.value) {
                FinancialYearHelper.getCurrentFinancialYearId().then(currentFinancialYearId => {
                    this.form.FinancialYearId.value = currentFinancialYearId.toString();
                });
            }
                this.getNextNumber();
                this.setDialogsLoadedState();
        }
        else {
            var placeOfSupplyStateId = (await StatesRow.getLookupAsync()).items.find(x => x.StateName == this.form.PlaceOfSupplyStateName.value)?.StateId;

            var userSupplyStateId = getRemoteData("UserData").PlaceOfSupplyStateId;
            this.form.PurchaseOrderDetailsList.SetPlaceOfSupply(placeOfSupplyStateId == userSupplyStateId ? true : false);

            this.setDialogsLoadedState();
        }
    }

    //--Cloning a Document (Save As)--
    protected updateInterface() {

        super.updateInterface();
        this.cloneButton.toggle(this.isEditMode());
    }

    protected getCloningEntity() {
        var clonedEntity = super.getCloningEntity();
        return clonedEntity;
    }

    //--Document Number Generation--
    private getNextNumber(): any {

        if (this.docType == null) {
            this.docType = DocumentsRow.getLookup().items.filter(a => a.DocumentName == "Purchase Orders")[0].DocumentShortName;
        }
        var prefix = this.getNextNumberPrefix(this.docType, this.form.FinancialYearId.text);

        this.form.PurchaseOrderNo.value = prefix;
    }

    protected validateBeforeSave() {
        if (!this.form.PurchaseOrderDetailsList.value || this.form.PurchaseOrderDetailsList.value.length === 0) {
            alertDialog("PurchaseOrder cannot be saved because no items have been added. Please add at least one item to proceed.");
            return false;
        }
        return true;
    }

    getToolbarButtons() {
        var buttons = super.getToolbarButtons();

        buttons.push({
            title: 'Print',
            icon: 'fas fa-file-pdf text-danger',
            cssClass: 'print-button',

            onClick: () => {
                ReportHelper.execute({
                    reportKey: 'PurchaseOrder',
                    params: {
                        ID: this.entityId
                    }
                });
            }
        });
        return buttons;
    }
    private clearVendorFields() {
        this.form.GSTIN.value = undefined;
        this.form.PlaceOfSupplyStateName.value = undefined;
        this.form.BillingAddress.value = undefined;
        this.form.BillingCityCityName.value = undefined;
        this.form.BillingPinCode.value = undefined;
        this.form.SupplyTypeId.value = undefined;
    }

    private clearShipToCustomerFields() {
        this.form.ShippingAddress.value = undefined;
        this.form.ShippingCityName.value = undefined;
        this.form.ShippingPinCode.value = undefined;
        this.form.ShippingGSTIN.value = undefined;
        this.form.ShippingPlaceOfSupplyStateName.value = undefined;
    }

    protected updateTitle() {
        this.dialogTitle = "New Purchase Order";
    }
} 