

namespace ReconnBooks.Common.ChatSql.Models
{
    public class ChatSqlRequest
    {
        [Required]
        public string Message { get; set; } = string.Empty;
        
        public string? ConversationId { get; set; }
        
        public bool IncludeSchema { get; set; } = false;
    }

    public class ChatSqlResponse
    {
        public bool Success { get; set; }
        
        public string? GeneratedSql { get; set; }
        
        public object? QueryResult { get; set; }
        
        public string? ErrorMessage { get; set; }
        
        public string? ConversationId { get; set; }
        
        public ChatSqlMetadata? Metadata { get; set; }
    }

    public class ChatSqlMetadata
    {
        public int ExecutionTimeMs { get; set; }
        
        public int RowCount { get; set; }
        
        public string[]? ColumnNames { get; set; }
        
        public bool WasQueryModified { get; set; }
        
        public string? OriginalQuery { get; set; }
        
        public string? ModificationReason { get; set; }
    }

    public class DatabaseSchemaInfo
    {
        public List<TableInfo> Tables { get; set; } = new();
        
        public DateTime LastUpdated { get; set; }
        
        public string DatabaseName { get; set; } = string.Empty;
    }

    public class TableInfo
    {
        public string TableName { get; set; } = string.Empty;
        
        public string Schema { get; set; } = "dbo";
        
        public List<ColumnInfo> Columns { get; set; } = new();
        
        public List<string> PrimaryKeys { get; set; } = new();
        
        public List<ForeignKeyInfo> ForeignKeys { get; set; } = new();
        
        public string? Description { get; set; }
    }

    public class ColumnInfo
    {
        public string ColumnName { get; set; } = string.Empty;
        
        public string DataType { get; set; } = string.Empty;
        
        public bool IsNullable { get; set; }
        
        public bool IsIdentity { get; set; }
        
        public int? MaxLength { get; set; }
        
        public int? Precision { get; set; }
        
        public int? Scale { get; set; }
        
        public string? DefaultValue { get; set; }
        
        public string? Description { get; set; }
    }

    public class ForeignKeyInfo
    {
        public string ConstraintName { get; set; } = string.Empty;
        
        public string ColumnName { get; set; } = string.Empty;
        
        public string ReferencedTable { get; set; } = string.Empty;
        
        public string ReferencedColumn { get; set; } = string.Empty;
        
        public string ReferencedSchema { get; set; } = "dbo";
    }

    public class ChatSqlConfiguration
    {
        public OpenAIConfig OpenAI { get; set; } = new();
        
        public AzureOpenAIConfig AzureOpenAI { get; set; } = new();
        
        public string Provider { get; set; } = "OpenAI";
        
        public int MaxQueryExecutionTimeSeconds { get; set; } = 30;
        
        public List<string> AllowedTables { get; set; } = new();
        
        public List<string> BlockedKeywords { get; set; } = new();
        
        public int MaxResultRows { get; set; } = 1000;
    }

    public class OpenAIConfig
    {
        public string ApiKey { get; set; } = string.Empty;
        
        public string Model { get; set; } = "gpt-4";
        
        public int MaxTokens { get; set; } = 1000;
        
        public float Temperature { get; set; } = 0.1f;
    }

    public class AzureOpenAIConfig
    {
        public string Endpoint { get; set; } = string.Empty;
        
        public string ApiKey { get; set; } = string.Empty;
        
        public string DeploymentName { get; set; } = string.Empty;
        
        public string ApiVersion { get; set; } = "2024-02-01";
    }

    public class QueryValidationResult
    {
        public bool IsValid { get; set; }
        
        public string? ErrorMessage { get; set; }
        
        public string? ModifiedQuery { get; set; }
        
        public List<string> Warnings { get; set; } = new();
        
        public bool WasModified { get; set; }
        
        public string? ModificationReason { get; set; }
    }

    public class ConversationContext
    {
        public string ConversationId { get; set; } = string.Empty;
        
        public List<ChatMessage> Messages { get; set; } = new();
        
        public DateTime CreatedAt { get; set; }
        
        public DateTime LastActivity { get; set; }
        
        public string UserId { get; set; } = string.Empty;
    }

    public class ChatMessage
    {
        public string Role { get; set; } = string.Empty; // "user" or "assistant"
        
        public string Content { get; set; } = string.Empty;
        
        public DateTime Timestamp { get; set; }
        
        public string? SqlQuery { get; set; }
        
        public object? QueryResult { get; set; }
    }
}
