using System.Data;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Serenity.Data;
using ReconnBooks.Common.ChatSql.Models;

namespace ReconnBooks.Common.ChatSql.Services
{
    public interface IDatabaseSchemaService
    {
        Task<DatabaseSchemaInfo> GetSchemaAsync();
        Task<string> GetSchemaAsTextAsync();
        Task RefreshSchemaAsync();
        Task<bool> IsTableAllowedAsync(string tableName);
    }

    public class DatabaseSchemaService : IDatabaseSchemaService
    {
        private readonly ITwoLevelCache _cache;
        private readonly ISqlConnections _connections;
        private readonly IOptions<ChatSqlConfiguration> _config;
        private readonly ILogger<DatabaseSchemaService> _logger;
        private const string SCHEMA_CACHE_KEY = "ChatSql_DatabaseSchema";
        private const string SCHEMA_TEXT_CACHE_KEY = "ChatSql_DatabaseSchemaText";
        private static readonly TimeSpan CacheExpiration = TimeSpan.FromHours(6);

        public DatabaseSchemaService(
            ITwoLevelCache cache,
            ISqlConnections connections,
            IOptions<ChatSqlConfiguration> config,
            ILogger<DatabaseSchemaService> logger)
        {
            _cache = cache;
            _connections = connections;
            _config = config;
            _logger = logger;
        }

        public async Task<DatabaseSchemaInfo> GetSchemaAsync()
        {
            return await Task.FromResult(_cache.GetLocalStoreOnly(SCHEMA_CACHE_KEY, CacheExpiration, "", () =>
            {
                return FetchSchemaFromDatabaseAsync().Result;
            }));
        }

        public async Task<string> GetSchemaAsTextAsync()
        {
            return await Task.FromResult(_cache.GetLocalStoreOnly(SCHEMA_TEXT_CACHE_KEY, CacheExpiration, "", () =>
            {
                var schema = GetSchemaAsync().Result;
                return ConvertSchemaToText(schema);
            }));
        }

        public async Task RefreshSchemaAsync()
        {
            _cache.Remove(SCHEMA_CACHE_KEY);
            _cache.Remove(SCHEMA_TEXT_CACHE_KEY);
            await GetSchemaAsync(); // Refresh cache
        }

        public async Task<bool> IsTableAllowedAsync(string tableName)
        {
            var allowedTables = _config.Value.AllowedTables;

            // If no allowed tables specified, allow all tables
            if (allowedTables == null || !allowedTables.Any())
                return true;

            return allowedTables.Contains(tableName, StringComparer.OrdinalIgnoreCase);
        }

        private async Task<DatabaseSchemaInfo> FetchSchemaFromDatabaseAsync()
        {
            try
            {
                using var connection = _connections.NewByKey("Default");

                var schema = new DatabaseSchemaInfo
                {
                    DatabaseName = "ReconnBooks", // Set a default name since we can't easily get it from Serenity
                    LastUpdated = DateTime.UtcNow,
                    Tables = new List<TableInfo>()
                };

                // Get all tables using SQL query instead of GetSchemaAsync
                var tablesQuery = @"
                    SELECT TABLE_NAME, TABLE_SCHEMA
                    FROM INFORMATION_SCHEMA.TABLES
                    WHERE TABLE_TYPE = 'BASE TABLE'
                    AND TABLE_SCHEMA NOT IN ('sys', 'information_schema')
                    ORDER BY TABLE_SCHEMA, TABLE_NAME";

                var tables = connection.Query(tablesQuery);

                foreach (var tableRow in tables)
                {
                    var tableName = tableRow.TABLE_NAME?.ToString();
                    var tableSchema = tableRow.TABLE_SCHEMA?.ToString() ?? "dbo";

                    if (string.IsNullOrEmpty(tableName) || IsSystemTable(tableName))
                        continue;

                    if (!await IsTableAllowedAsync(tableName))
                        continue;

                    var tableInfo = new TableInfo
                    {
                        TableName = tableName,
                        Schema = tableSchema,
                        Columns = await GetColumnsForTableAsync(connection, tableName, tableSchema),
                        PrimaryKeys = await GetPrimaryKeysForTableAsync(connection, tableName, tableSchema),
                        ForeignKeys = await GetForeignKeysForTableAsync(connection, tableName, tableSchema)
                    };

                    schema.Tables.Add(tableInfo);
                }

                _logger.LogInformation("Database schema fetched successfully. Found {TableCount} tables.", schema.Tables.Count);
                return schema;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching database schema");
                throw;
            }
        }

        private async Task<List<ColumnInfo>> GetColumnsForTableAsync(IDbConnection connection, string tableName, string schema)
        {
            var columns = new List<ColumnInfo>();

            try
            {
                var columnsQuery = @"
                    SELECT
                        COLUMN_NAME,
                        DATA_TYPE,
                        IS_NULLABLE,
                        CHARACTER_MAXIMUM_LENGTH,
                        NUMERIC_PRECISION,
                        NUMERIC_SCALE,
                        COLUMN_DEFAULT,
                        COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') AS IS_IDENTITY
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_NAME = @tableName AND TABLE_SCHEMA = @schema
                    ORDER BY ORDINAL_POSITION";

                var columnRows = connection.Query(columnsQuery, new { tableName, schema });

                foreach (var columnRow in columnRows)
                {
                    var columnInfo = new ColumnInfo
                    {
                        ColumnName = columnRow.COLUMN_NAME?.ToString() ?? "",
                        DataType = columnRow.DATA_TYPE?.ToString() ?? "",
                        IsNullable = columnRow.IS_NULLABLE?.ToString()?.Equals("YES", StringComparison.OrdinalIgnoreCase) ?? false,
                        MaxLength = columnRow.CHARACTER_MAXIMUM_LENGTH as int?,
                        Precision = columnRow.NUMERIC_PRECISION as int?,
                        Scale = columnRow.NUMERIC_SCALE as int?,
                        DefaultValue = columnRow.COLUMN_DEFAULT?.ToString(),
                        IsIdentity = Convert.ToBoolean(columnRow.IS_IDENTITY ?? 0)
                    };

                    columns.Add(columnInfo);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error getting columns for table {TableName}", tableName);
            }

            return columns;
        }

        private async Task<List<string>> GetPrimaryKeysForTableAsync(IDbConnection connection, string tableName, string schema)
        {
            var primaryKeys = new List<string>();

            try
            {
                // SQL Server specific query for primary keys
                var sql = @"
                    SELECT COLUMN_NAME
                    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                    WHERE OBJECTPROPERTY(OBJECT_ID(CONSTRAINT_SCHEMA + '.' + CONSTRAINT_NAME), 'IsPrimaryKey') = 1
                    AND TABLE_NAME = @tableName AND TABLE_SCHEMA = @schema
                    ORDER BY ORDINAL_POSITION";

                var keyRows = connection.Query(sql, new { tableName, schema });

                foreach (var keyRow in keyRows)
                {
                    primaryKeys.Add(keyRow.COLUMN_NAME?.ToString() ?? "");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error getting primary keys for table {TableName}", tableName);
            }

            return primaryKeys;
        }

        private async Task<List<ForeignKeyInfo>> GetForeignKeysForTableAsync(IDbConnection connection, string tableName, string schema)
        {
            var foreignKeys = new List<ForeignKeyInfo>();

            try
            {
                // SQL Server specific query for foreign keys
                var sql = @"
                    SELECT
                        fk.name AS ConstraintName,
                        c1.name AS ColumnName,
                        t2.name AS ReferencedTable,
                        c2.name AS ReferencedColumn,
                        s2.name AS ReferencedSchema
                    FROM sys.foreign_keys fk
                    INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
                    INNER JOIN sys.tables t1 ON fk.parent_object_id = t1.object_id
                    INNER JOIN sys.schemas s1 ON t1.schema_id = s1.schema_id
                    INNER JOIN sys.columns c1 ON fkc.parent_object_id = c1.object_id AND fkc.parent_column_id = c1.column_id
                    INNER JOIN sys.tables t2 ON fkc.referenced_object_id = t2.object_id
                    INNER JOIN sys.schemas s2 ON t2.schema_id = s2.schema_id
                    INNER JOIN sys.columns c2 ON fkc.referenced_object_id = c2.object_id AND fkc.referenced_column_id = c2.column_id
                    WHERE t1.name = @tableName AND s1.name = @schema";

                var fkRows = connection.Query(sql, new { tableName, schema });

                foreach (var fkRow in fkRows)
                {
                    foreignKeys.Add(new ForeignKeyInfo
                    {
                        ConstraintName = fkRow.ConstraintName?.ToString() ?? "",
                        ColumnName = fkRow.ColumnName?.ToString() ?? "",
                        ReferencedTable = fkRow.ReferencedTable?.ToString() ?? "",
                        ReferencedColumn = fkRow.ReferencedColumn?.ToString() ?? "",
                        ReferencedSchema = fkRow.ReferencedSchema?.ToString() ?? ""
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error getting foreign keys for table {TableName}", tableName);
            }

            return foreignKeys;
        }

        private string ConvertSchemaToText(DatabaseSchemaInfo schema)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"Database: {schema.DatabaseName}");
            sb.AppendLine($"Last Updated: {schema.LastUpdated:yyyy-MM-dd HH:mm:ss} UTC");
            sb.AppendLine();

            foreach (var table in schema.Tables.OrderBy(t => t.TableName))
            {
                sb.AppendLine($"Table: {table.Schema}.{table.TableName}");

                if (table.PrimaryKeys.Any())
                {
                    sb.AppendLine($"  Primary Keys: {string.Join(", ", table.PrimaryKeys)}");
                }

                sb.AppendLine("  Columns:");
                foreach (var column in table.Columns.OrderBy(c => c.ColumnName))
                {
                    var nullable = column.IsNullable ? "NULL" : "NOT NULL";
                    var identity = column.IsIdentity ? " IDENTITY" : "";
                    var length = column.MaxLength.HasValue ? $"({column.MaxLength})" : "";

                    sb.AppendLine($"    {column.ColumnName} {column.DataType}{length} {nullable}{identity}");
                }

                if (table.ForeignKeys.Any())
                {
                    sb.AppendLine("  Foreign Keys:");
                    foreach (var fk in table.ForeignKeys)
                    {
                        sb.AppendLine($"    {fk.ColumnName} -> {fk.ReferencedSchema}.{fk.ReferencedTable}.{fk.ReferencedColumn}");
                    }
                }

                sb.AppendLine();
            }

            return sb.ToString();
        }

        private static bool IsSystemTable(string tableName)
        {
            var systemTables = new[]
            {
                "sysdiagrams", "__EFMigrationsHistory", "__MigrationHistory",
                "AspNetUsers", "AspNetRoles", "AspNetUserRoles", "AspNetUserClaims",
                "AspNetUserLogins", "AspNetUserTokens", "AspNetRoleClaims"
            };

            return systemTables.Any(st => tableName.StartsWith(st, StringComparison.OrdinalIgnoreCase));
        }
    }
}
