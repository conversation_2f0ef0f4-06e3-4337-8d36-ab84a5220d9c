import { CustomersRow, DocumentsRow, ProformaInvoicesForm, ProformaInvoicesRow, ProformaInvoicesService, StatesRow, CitiesRow, SupplyTypesRow } from '@/ServerTypes/Default';
import { Decorators, formatDate, toId, getRemoteData, alertDialog, WidgetProps } from '@serenity-is/corelib';
import { FinancialYearHelper } from '../FinancialYears/FinancialYearHelper';
import { VerifyAuthorizeDialog } from '../../Common/Helpers/VerifyAuthorizeDialog';
import { ReportHelper } from '@serenity-is/extensions';

@Decorators.registerClass('ReconnBooks.Default.ProformaInvoicesDialog')
@Decorators.responsive()
@Decorators.panel()
export class ProformaInvoicesDialog extends VerifyAuthorizeDialog<ProformaInvoicesRow> {
    protected getFormKey() { return ProformaInvoicesForm.formKey; }
    protected getRowDefinition() { return ProformaInvoicesRow; }
    protected getService() { return ProformaInvoicesService.baseUrl; }

    protected form = new ProformaInvoicesForm(this.idPrefix);
    private docType: string;
    constructor(props: WidgetProps<any>) {
        super(props);

        (this.form.ProformaInvoiceDetailsList.view as any).onRowsOrCountChanged.subscribe((e) => {
            e.stopPropagation();
            this.updateTotal();
            this.form.ProformaInvoiceDetailsList.getGrid().focus();
            // Add your custom logic here
        });

        (this.form.ProformaInvoiceDetailsList.view as any).onDataChanged.subscribe((e) => {
            e.preventDefault = false;
            this.updateTotal();
            // Add your custom logic here
        });

        this.form.CustomerId.change(async a => {
            //setTimeout(async () => {
                var CustomerId = toId(this.form.CustomerId.value);
                if (CustomerId != null) {
                    var customer = (await CustomersRow.getLookupAsync()).itemById[CustomerId];
                    this.form.GSTIN.value = customer.GSTIN;
                    this.form.PlaceOfSupplyStateName.value = customer.PlaceOfSupplyStateName;
                    this.form.BillingAddress.value = customer.BillingAddress;
                    this.form.BillingCityCityName.value = customer.BillingCityCityName;
                    this.form.BillingPinCode.value = customer.BillingPinCode;
                    this.form.SupplyTypeId.value = customer.SupplyTypeId.toString();

                    var userSupplyStateId = getRemoteData("UserData").PlaceOfSupplyStateId;
                    this.form.ProformaInvoiceDetailsList.SetPlaceOfSupply(customer.PlaceOfSupplyId == userSupplyStateId ? true : false);
                }
                else {
                    this.clearCustomerFields();
                }
            //}, 100);
        })

        this.form.SalesOrderId.changeSelect2(e => {
            if (this.form.SalesOrderId.value === '') {
                // Clear the details in the grid
                this.form.ProformaInvoiceDetailsList.value = [];
            }
            else {
                ProformaInvoicesService.GetFromSalesOrderDetails({
                    EntityId: toId(this.form.SalesOrderId.value)
                },
                    response => {
                        this.form.ProformaInvoiceDetailsList.value = response.Entities;
                    });
            }
        });

        this.form.FinancialYearId.changeSelect2(e => {
            this.getNextNumber();
        });
        this.form.ShipToCustomerId.change(async a => {
            var ShipToCustomerId = toId(this.form.ShipToCustomerId.value);
            if (ShipToCustomerId != null) {
                var shiptocustomer = (await CustomersRow.getLookupAsync()).itemById[ShipToCustomerId];
                this.form.ShippingAddress.value = shiptocustomer.BillingAddress;
                this.form.ShippingCityName.value = shiptocustomer.BillingCityCityName;
                this.form.ShippingPinCode.value = shiptocustomer.BillingPinCode;
                this.form.ShippingGSTIN.value = shiptocustomer.GSTIN;
                this.form.ShippingPlaceOfSupplyStateName.value = shiptocustomer.PlaceOfSupplyStateName;
            }
            else {
                this.clearShipToCustomerFields();
            }
        })
    }

    private updateTotal(): void {
        let total = 0;
        for (let item of this.form.ProformaInvoiceDetailsList.view.getItems()) {
            total += item.NetAmount || 0;
        }
        this.form.ProformaInvoiceAmt.value = total;
        const roundOff = total - Math.floor(total);
        this.form.RoundingOff.value = parseFloat((roundOff >= 0.50 ? 1 - roundOff : -roundOff).toFixed(2));
        this.form.GrandTotal.value = this.form.ProformaInvoiceAmt.value + this.form.RoundingOff.value;
    }

    protected async afterLoadEntity() {
        super.afterLoadEntity();
        if (this.isNew()) {

            this.form.SupplyTypeId.value = (await SupplyTypesRow.getLookupAsync()).items.find(item => item.SetDefault)?.SupplyTypeId.toString();

            if (!this.form.FinancialYearId?.value) {
                FinancialYearHelper.getCurrentFinancialYearId().then(currentFinancialYearId => {
                    this.form.FinancialYearId.value = currentFinancialYearId.toString();
                });
            }

            this.getNextNumber();
            this.setDialogsLoadedState();
        }
        else {
            var placeOfSupplyStateId = (await StatesRow.getLookupAsync()).items.find(x => x.StateName == this.form.PlaceOfSupplyStateName.value)?.StateId;

            var userSupplyStateId = getRemoteData("UserData").PlaceOfSupplyStateId;
            this.form.ProformaInvoiceDetailsList.SetPlaceOfSupply(placeOfSupplyStateId == userSupplyStateId ? true : false);

            this.setDialogsLoadedState();
        }
    }

    protected updateInterface() {
        super.updateInterface();
        this.cloneButton.toggle(this.isEditMode());
    }

    protected getCloningEntity() {
        var clonedEntity = super.getCloningEntity();
        return clonedEntity;
    }

    private getNextNumber(): any {

        if (this.docType == null) {
            this.docType = DocumentsRow.getLookup().items.filter(a => a.DocumentName == "Proforma Invoices")[0].DocumentShortName;
        }

        var prefix = this.getNextNumberPrefix(this.docType, this.form.FinancialYearId.text);

        this.form.ProformaInvoiceNo.value = prefix;
    }

    protected validateBeforeSave() {
        if (!this.form.ProformaInvoiceDetailsList.value || this.form.ProformaInvoiceDetailsList.value.length === 0) {
            alertDialog("Proforma Invoice cannot be saved because no items have been added. Please add at least one item to proceed.");
            return false;
        }
        return true;
    }

    getToolbarButtons() {
        var buttons = super.getToolbarButtons();

        buttons.push({
            title: 'Print',
            icon: 'fas fa-file-pdf text-danger',
            cssClass: 'print-button',

            onClick: () => {
                ReportHelper.execute({
                    reportKey: 'ProformaInvoiceReport',
                    params: {
                        ID: this.entityId
                    }
                });
            }
        });
        return buttons;
    }
    private clearCustomerFields() {
        this.form.GSTIN.value = undefined;
        this.form.PlaceOfSupplyStateName.value = undefined;
        this.form.BillingAddress.value = undefined;
        this.form.BillingCityCityName.value = undefined;
        this.form.BillingPinCode.value = undefined;
        this.form.SupplyTypeId.value = undefined;
        this.form.ProformaInvoiceDetailsList.value = undefined;
    }

    private clearShipToCustomerFields() {
        this.form.ShippingAddress.value = undefined;
        this.form.ShippingCityName.value = undefined;
        this.form.ShippingPinCode.value = undefined;
        this.form.ShippingGSTIN.value = undefined;
        this.form.ShippingPlaceOfSupplyStateName.value = undefined;
    }

    protected updateTitle() {
        this.dialogTitle = "New Proforma Invoice";
    }
} 